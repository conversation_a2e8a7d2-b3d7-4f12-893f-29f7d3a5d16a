<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.SjjsImportDataResultDao">
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.SjjsImportDataResult" >
        <result column="TB_NAME_EN" property="tbNameEn" jdbcType="VARCHAR" />
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="FLAG" property="flag" jdbcType="VARCHAR"/>
        <result column="RESULT_MSG" property="resultMsg" jdbcType="VARCHAR"/>
        <result column="CAUSE_MSG" property="causeMsg" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        TB_NAME_EN,CREATE_TIME,FLAG,RESULT_MSG,CAUSE_MSG
    </sql>


    <insert id="insertSjjsImportDataResult"  parameterType="com.example.psbc.entity.SjjsImportDataResult">
        insert into T_SJJS_IMPORT_DATA_RESULT(
            <include refid="Base_Column_List"/>
        )
        values (
            #{tbNameEn,jdbcType=VARCHAR},
            now(),
            #{flag,jdbcType=VARCHAR},
            #{resultMsg,jdbcType=VARCHAR},
            #{causeMsg,jdbcType=VARCHAR}
        );
    </insert>



</mapper>