package com.example.psbc;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling
@MapperScan("com.example.psbc.dao")
//@EnableAutoConfiguration(exclude={DataSourceAutoConfiguration.class})
public class PsbcObjApplication {

	public static void main(String[] args) {
		SpringApplication.run(PsbcObjApplication.class, args);
		System.out.println("数据集市启动成功");
	}

}
