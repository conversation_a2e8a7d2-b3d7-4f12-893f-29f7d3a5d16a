package com.example.psbc.services.impl;

import com.example.psbc.dao.SjjsTargetConfigDao;
import com.example.psbc.entity.SjjsTargetConfig;
import com.example.psbc.services.SjjsTargetConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("sjjsTargetConfigService")
public class SjjsTargetConfigServiceImpl implements SjjsTargetConfigService {

    @Autowired
    private SjjsTargetConfigDao sjjsTargetConfigDao;
    @Override
    public List<SjjsTargetConfig> selectListByParam(SjjsTargetConfig sjjsTargetConfig) {
        return sjjsTargetConfigDao.selectListByParam(sjjsTargetConfig);
    }
}
