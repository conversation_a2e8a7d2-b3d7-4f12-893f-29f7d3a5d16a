<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.SjjsDataChkDao">
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.SjjsDataChk" >
        <id column="file_id" property="fileId" jdbcType="VARCHAR" />
        <result column="FILE_PATH" property="filePath" jdbcType="VARCHAR" />
        <result column="FILE_HEAD" property="fileHead" jdbcType="VARCHAR"/>
        <result column="FILE_DATE" property="fileDate" jdbcType="VARCHAR"/>
        <result column="FILE_TAIL" property="fileTail" jdbcType="VARCHAR" />
        <result column="DATE_TYPE" property="dateType" jdbcType="VARCHAR" />
        <result column="PROC_NAME" property="procName" jdbcType="VARCHAR"/>
        <result column="PROC_TYPE" property="procType" jdbcType="VARCHAR"/>
        <result column="FILE_TXT" property="fileTxt" jdbcType="VARCHAR"/>
        <result column="FILE_SEQ" property="fileSeq" jdbcType="INTEGER"/>
        <result column="CHK_NUM" property="chkNum" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="UPDATE_DATE" property="updateDate" jdbcType="DATE" />
        <result column="DATE_KEY" property="dateKey" jdbcType="VARCHAR" />
        <result column="DEL_FLAG" property="delFlag" jdbcType="VARCHAR" />

    </resultMap>

    <sql id="Base_Column_List">
        file_id,FILE_PATH,FILE_HEAD,FILE_DATE,FILE_TAIL,
        DATE_TYPE,PROC_NAME,PROC_TYPE,FILE_TXT,FILE_SEQ,
        CHK_NUM,STATUS,REMARK,UPDATE_DATE,DATE_KEY,DEL_FLAG
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from T_SJJS_DATA_CHK
        where file_id = #{fileId,jdbcType=VARCHAR}
    </select>
    <select id="selectExecTaskList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SJJS_DATA_CHK where status in ('0','2')  order by FILE_SEQ
    </select>

    <select id="selectListByParam" resultMap="BaseResultMap" parameterType="com.example.psbc.entity.SjjsDataChk">
        select
        <include refid="Base_Column_List"/>
        from T_SJJS_DATA_CHK
        where 1=1
        <if test="status != null">
            and status=#{status}
        </if>
        order by FILE_SEQ
    </select>
    <update id="updateSjjsDataChk" parameterType="com.example.psbc.entity.SjjsDataChk">
        update T_SJJS_DATA_CHK set
        <if test="fileDate != null">
            FILE_DATE=#{fileDate},
        </if>
        <if test="status != null">
            status=#{status},
        </if>
        <if test="remark != null">
            remark=#{remark},
        </if>
        update_date=NOW()
        where
        file_id=#{fileId}
    </update>


</mapper>