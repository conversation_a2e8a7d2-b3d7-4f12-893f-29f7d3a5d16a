package com.example.psbc.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.psbc.controller.ObjectConfig;
import com.example.psbc.controller.ObjectData;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Created by test on 2022/7/11.
 */
public class TypeConversionUtil {
    //将接口读取到的报文转成list
    public static  String  jsonToList(JSONObject resJsonObject, List<String> data_list){
        String code = resJsonObject.getString("code");
        String msg = resJsonObject.getString("message");
        //log.info("响应报文code值为:" + code+"响应报文message值为:"+msg);
        if (ConstantUtil.API_RES_CODE.SUCCESS.getValue().equals(code)){
            JSONArray jsonArray = (JSONArray) resJsonObject.get("data");
            if (jsonArray != null && jsonArray.size() > 0){
                for (int j = 0; j < jsonArray.size(); j++){
                    JSONObject jsonObject = jsonArray.getJSONObject(j);
                    StringBuffer row = new StringBuffer();
                    Iterator keys = jsonObject.keySet().iterator();
                    while (keys.hasNext()){
                        // 获得key
                        Object key = keys.next();
                        Object value = jsonObject.get(key);
                        if ("null".equals(value) || "null ".equals(value) || " null ".equals(value) || value == null){
                            value = "";
                        }
                        row.append(value).append(ConstantUtil.SEPARATOR);//拼接分隔符
                    }
                    data_list.add(row.toString());
                }
            }
        }else{
            return null;
        }

        return msg;
    }


    public   ArrayList<ObjectData>  jsonToList(List<ArrayList> excelDataList,String tj_mon) {
        int index=10;//固定列个数
        ArrayList<ObjectData>  result_data= new ArrayList<ObjectData>();//用来封装成最终存储的结构
        ArrayList<ObjectConfig> objectList=new ArrayList<ObjectConfig>();//读取项目配置表
        for(int j=0;j<excelDataList.size();j++) {
            ArrayList arrayList=excelDataList.get(j);
            for (int i = 0; i < objectList.size(); i++) {
                ObjectConfig objectConfig=objectList.get(i);
                ObjectData objectData=new ObjectData();
                objectData.setTj_mon(tj_mon);    //月份 //execlDataList.get(0).get(0).toString()
                objectData.setSfz(arrayList.get(0).toString());    //身份证
                objectData.setObject_name(objectConfig.getC_name()); //项目中文名称
                objectData.setValue(arrayList.get(i+index).toString());//值
                result_data.add(objectData);
            }
        }
        return  result_data;
    }
}
