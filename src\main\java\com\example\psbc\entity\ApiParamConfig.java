package com.example.psbc.entity;

import java.util.Date;

/**
 * Created by xmpsb-xiaohj on 2021/10/22.
 */
public class ApiParamConfig {
    private Double id;

    private String url;

    private String cycle;

    private String execDate;

    private String status;

    private Date updateDate;

    private String reqParam;

    private String  tbName;

    private String remark;
    private String  fileCreatePath;
    private String fileTargetPath;
    private String  prefFileName;//PREF_FILE_NAME
    private String  getWay;//获取方式(API或者FILE)

    private String isPage;//是否分页
    private Integer pageSize;//每页多少条数据
    private String countUrl;
    private String taskInfoUrl;
    private String emptyFileFlag;//是否可为空文件

    public String getEmptyFileFlag(){
        return emptyFileFlag;
    }

    public void setEmptyFileFlag(String emptyFileFlag) {
        this.emptyFileFlag = emptyFileFlag;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getIsPage() {
        return isPage;
    }

    public void setIsPage(String isPage) {
        this.isPage = isPage;
    }

    public String getCountUrl() {
        return countUrl;
    }

    public void setCountUrl(String countUrl) {
        this.countUrl = countUrl;
    }

    public String getTaskInfoUrl() {
        return taskInfoUrl;
    }

    public void setTaskInfoUrl(String taskInfoUrl) {
        this.taskInfoUrl = taskInfoUrl;
    }


    public String getGetWay() {
        return getWay;
    }

    public void setGetWay(String getWay) {
        this.getWay = getWay;
    }

    public Double getId() {
        return id;
    }

    public void setId(Double id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCycle() {
        return cycle;
    }

    public void setCycle(String cycle) {
        this.cycle = cycle;
    }

    public String getExecDate() {
        return execDate;
    }

    public void setExecDate(String execDate) {
        this.execDate = execDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getReqParam() {
        return reqParam;
    }

    public void setReqParam(String reqParam) {
        this.reqParam = reqParam;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFileCreatePath() {
        return fileCreatePath;
    }

    public void setFileCreatePath(String fileCreatePath) {
        this.fileCreatePath = fileCreatePath;
    }

    public String getFileTargetPath() {
        return fileTargetPath;
    }

    public void setFileTargetPath(String fileTargetPath) {
        this.fileTargetPath = fileTargetPath;
    }

    public String getPrefFileName() {
        return prefFileName;
    }

    public void setPrefFileName(String prefFileName) {
        this.prefFileName = prefFileName;
    }


}
