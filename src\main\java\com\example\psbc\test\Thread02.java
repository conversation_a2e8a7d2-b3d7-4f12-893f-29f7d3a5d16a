package com.example.psbc.test;

/**
 * Created by xmpsb-xiaohj on 2022/4/22.
 */
public class Thread02  extends Thread{
   /* private  Thread  otherThread;
    Thread02(Thread  t){
        otherThread=t;
    }*/

    @Override
    public void run(){
        try {
            Thread.sleep(5*1000);
            System.out.println("线程Thread02开始执行，线程名字:"+Thread.currentThread().getName());
        }catch (Exception ex){
            ex.printStackTrace();
        }
        System.out.println("线程Thread02结束，线程名字:"+Thread.currentThread().getName());

    }
}
