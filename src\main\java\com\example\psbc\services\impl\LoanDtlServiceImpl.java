package com.example.psbc.services.impl;

import com.example.psbc.dao.LoanDtlDao;
import com.example.psbc.entity.LoanDtl;
import com.example.psbc.services.LoanDtlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by xmpsb-xiaohj on 2021/11/3.
 */
@Service("loanDtlService")
public class LoanDtlServiceImpl  implements LoanDtlService {


    @Autowired
    private   LoanDtlDao  loanDtlDao;
    @Override
    public List<LoanDtl> getLoanDtlByTxnDate(String txnDate) {
        return loanDtlDao.getLoanDtlByTxnDate(txnDate);
    }
}
