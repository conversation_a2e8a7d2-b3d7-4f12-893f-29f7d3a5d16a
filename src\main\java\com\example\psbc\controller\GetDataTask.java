package com.example.psbc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.example.psbc.entity.ApiParamConfig;
import com.example.psbc.entity.KeyParam;
import com.example.psbc.services.ApiParamConfigService;
import com.example.psbc.services.KeyParamService;
import com.example.psbc.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by  on 2022/5/23.
 * 功能描述：调用数据集市API
 */
@Configuration
public class GetDataTask{
    @Autowired
    private ApiParamConfigService apiParamConfigService;
    private final Logger log = LoggerFactory.getLogger(this.getClass());
     void dealTask(){
        log.info("====定时任务开始执行（调用API）======");
        System.setProperty("jdk.tls.allowUnsafeServerCertChange","true");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation","true");
        System.setProperty("jdk.tls.client.protocols","TLSv1,TLSv1.1,TLSv1.2");
        log.info("==============成功配置系统参数=====================");
        Vector<ApiParamConfig> list = apiParamConfigService.selectExecTaskList();//获取状态为成功(0)或者失败(2)的任务
        if(list !=null && list.size()>0){
            for (int i = 0; i< list.size(); i++){
                ApiParamConfig apiParamConfig= list.get(i);
                try{
                    //处理线程并发问题
                    synchronized (ApiParamConfig.class){
                        Double id=apiParamConfig.getId();
                        String nowExecDate=apiParamConfig.getExecDate();
                        ApiParamConfig nowAPC=apiParamConfigService.selectByPrimaryKey(id);
                        String  status=nowAPC.getStatus();
                        if(ConstantUtil.TASK_STATUS.EXEC.getValue().equals(status)
                                || !nowExecDate.equals(apiParamConfig.getExecDate())){
                            log.info("其他线程正常处理该接口"+apiParamConfig.getUrl());
                            continue;
                        }else{
                            apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.EXEC.getValue());//执行中
                            apiParamConfig.setRemark("执行中");
                            apiParamConfigService.updateApiParamConfig(apiParamConfig);
                        }
                    }
                    if(ConstantUtil.DATA_GET_WAY.API.getValue().equals(apiParamConfig.getGetWay())){
                        String execDate=apiParamConfig.getExecDate();
                        String taskInfoUrl=apiParamConfig.getTaskInfoUrl();
                        JSONObject jsonObj= CallInterfaceUtil.postUrlData(taskInfoUrl,null);
                        log.info("======获取任务信息的URL:"+taskInfoUrl.replace("?",",")+"日期"+execDate);
                        log.info("响应结果:"+jsonObj.toJSONString());
                        String resCode=jsonObj.getString("code");
                        if(!ConstantUtil.API_RES_CODE.SUCCESS.getValue().equals(resCode)){
                            apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());//失败
                            apiParamConfig.setRemark("错误代码:"+resCode+"；错误信息:"+jsonObj.getString("message"));
                            apiParamConfigService.updateApiParamConfig(apiParamConfig);
                            log.info(taskInfoUrl+"；错误代码:"+resCode+"；错误信息:"+jsonObj.getString("message"));
                            continue;
                        }
                        JSONObject dataObj=jsonObj.getJSONObject("data");
                        String   lastJobStatus=dataObj.getString("lastJobStatus");
                        String  lastTxDate=dataObj.getString("lastTxDate");

                        Date taskLastDate= DateUtil.formatToDate(lastTxDate);//数据集市的最大作业日期
                        Date execDt=DateUtil.formatToDate(execDate);//
                        if(execDt.equals(taskLastDate) && !"Done".equals(lastJobStatus)){
                            apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.SUCCESS.getValue());
                            apiParamConfig.setRemark("作业状态lastJobStatus:"+lastJobStatus);
                            apiParamConfigService.updateApiParamConfig(apiParamConfig);
                            log.info("作业状态lastJobStatus:"+lastJobStatus);
                            continue;
                        }else if(taskLastDate.before(execDt)){
                            apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.SUCCESS.getValue());
                            apiParamConfig.setRemark("执行日期大于数据集市的最大作业日期");
                            apiParamConfigService.updateApiParamConfig(apiParamConfig);
                            log.info("执行日期大于数据集市的最大作业日期");
                            continue;
                        }

                        //log.info("Url地址:"+apiParamConfig.getUrl()+"执行日期:"+execDate);
                        String reqParam=apiParamConfig.getReqParam();
                        HashMap<String, Object> paramMap = new HashMap<>();
                        String isPage=apiParamConfig.getIsPage();//是否分页
                        List<String> data_list=new CopyOnWriteArrayList<>();//new ArrayList();//用来封装数据
                        String reply_msg="";//接口响应消息
                        String  cycle=apiParamConfig.getCycle();
                        //是否分页获取数据
                        if(ConstantUtil.IS_PAGE.YES.getValue().equals(isPage)){
                            if(reqParam!=null && !"".equals(reqParam)){
                                String txDate="'"+execDate.substring(0,4)+"-"+execDate.substring(4,6)+"-"+execDate.substring(6)+"'";
                                reqParam=reqParam.replace(ConstantUtil.PARAM_TX_DATE,txDate);
                                paramMap.put(ConstantUtil.UDC,reqParam);
                            }
                            String  countUrl=apiParamConfig.getCountUrl();
                            //调用接口获取总条数
                            JSONObject countJsonObject=CallInterfaceUtil.postUrlData(countUrl,paramMap);
                            log.info("======调用接口获取总条数"+countUrl.replace("?","")+"日期"+execDate);
                            log.info("响应信息:"+countJsonObject.toJSONString());
                            if(countJsonObject==null){
                                apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                apiParamConfig.setRemark("请求COUNT_URL,jsonObject为空");
                                apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                log.info("===请求:"+countUrl+"===");
                                log.info("请求COUNT_URL,jsonObject为空");
                                continue;
                            }else{
                               String code= countJsonObject.get("code").toString();
                               String message=countJsonObject.get("message").toString();
                               if(!ConstantUtil.API_RES_CODE.SUCCESS.getValue().equals(code)){
                                   apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                   apiParamConfig.setRemark(message);
                                   apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                   log.info("===请求:"+countUrl+"===报错信息："+message);
                                   continue;
                               }
                            }
                            JSONArray jsonArray=(JSONArray)countJsonObject.get("data");
                            if(jsonArray!=null && jsonArray.size()>0){
                                int total=(int)jsonArray.getJSONObject(0).get("total");
                                log.info("执行日期"+execDate+"，接口数据总条数total为"+total);
                                String emptyFileFlag=apiParamConfig.getEmptyFileFlag();//是否可为空文件标志
                                if(total==0 && ConstantUtil.IS_EMPTY_FILE.YES.getValue().equals(emptyFileFlag)){
                                    String  fileCreatePath=apiParamConfig.getFileCreatePath();
                                    String   prefFileName=apiParamConfig.getPrefFileName();
                                    String fileName=prefFileName+execDate+ConstantUtil.TXT;
                                    String  fileTargetPath=apiParamConfig.getFileTargetPath();
                                    File file=new File(fileCreatePath+fileName);
                                    //创建一个空文件
                                    if(!file.exists()){
                                        try{
                                            file.createNewFile();
                                        }catch (Exception ex){
                                            ex.printStackTrace();
                                        }
                                    }
                                    this.removeFile(fileName,fileCreatePath,fileTargetPath);//文件生成后转移至指定目录下
                                    apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.SUCCESS.getValue());
                                    setExecDate(cycle,execDate,apiParamConfig);//切换作业日期
                                    apiParamConfig.setRemark("本期数据为空"+execDate);
                                    apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                    continue;
                                }else if(total==0 && ConstantUtil.IS_EMPTY_FILE.NO.getValue().equals(emptyFileFlag)){
                                    apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.SUCCESS.getValue());
                                    apiParamConfig.setRemark("当前接口数据不可为空");
                                    apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                    continue;
                                }
                                int pageSize=apiParamConfig.getPageSize();
                                int  index=total % pageSize==0?(total/pageSize):(total/pageSize)+1;//循环次数（即页数）
                                boolean  isContinue=true;
                                int  errorIndex=0;
                                for(int j=1;j<=index;j++){
                                    String url=apiParamConfig.getUrl();
                                    String totalStr=total+"";
                                    String pageIndex=j+"";
                                    url=url.replace(ConstantUtil.URL_TOTAL_XXX,totalStr);//替换总数量占位符
                                    url=url.replace(ConstantUtil.URL_PI_XXX,pageIndex);//替换页码占位符
                                    JSONObject resJsonObject=CallInterfaceUtil.postUrlData(url,paramMap);//调用接口获取数据列表
                                    log.info("===GET DATA===:"+apiParamConfig.getUrl().replace("?","")+"日期"+execDate+"页码"+pageIndex);
                                    reply_msg= TypeConversionUtil.jsonToList(resJsonObject,data_list);//将数据封装成LIST
                                    if(!reply_msg.equals("ok")){
                                        //apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                        //apiParamConfig.setRemark("API分页取数中断,报文响应信息:"+reply_msg);
                                        //apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                        errorIndex=j;
                                        isContinue=false;
                                        break;
                                    }
                                    if(j==1 || j==index){
                                        //String jsonStr=resJsonObject.toJSONString();
                                        //值为null对应的key会被过滤掉；须要用到fastjson的SerializerFeature序列化属性
                                        String json=JSONObject.toJSONString(resJsonObject, SerializerFeature.WriteMapNullValue);
                                        if(json.length()>2000){
                                            log.info("接口响应报文（截取）,页码:"+j+":"+json.substring(0,2000));
                                        }else{
                                            log.info("接口响应报文jsonString,页码:"+j+":"+json);
                                        }
                                    }
                                }
                                if(!isContinue){
                                    apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                    apiParamConfig.setRemark(
                                            "API接口分页获取数据中断，" +
                                            "总记录数:"+total+",总页数:"+index+
                                                    ",每页条数:"+pageSize+",异常页码:"+errorIndex+",接口响应报文："+reply_msg);
                                    apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                    continue;
                                }
                            }else{
                                apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                apiParamConfig.setRemark("API接口获取数据为空!");
                                apiParamConfigService.updateApiParamConfig(apiParamConfig);
                            }
                        }else{
                            String  url;
                            if(reqParam!=null){
                                reqParam+=(execDate.substring(0,4)+"-"+execDate.substring(4,6)+"-"+execDate.substring(6));
                                url=apiParamConfig.getUrl()+reqParam;
                            }else{
                                url=apiParamConfig.getUrl();
                            }
                            JSONObject resJsonObject=CallInterfaceUtil.postUrlData(url,paramMap);
                            log.info("请求URL:"+url);
                            log.info("响应信息:"+resJsonObject==null?"null":resJsonObject.toJSONString());
                            if(resJsonObject!=null){
                                reply_msg=TypeConversionUtil.jsonToList(resJsonObject,data_list);//将数据封装成LIST
                                if(reply_msg==null){
                                    String msg = resJsonObject.get("message").toString();
                                    apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                    apiParamConfig.setRemark("请求URL,报文响应:"+msg);
                                    apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                    continue;
                                }
                            }else{
                                apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                                apiParamConfig.setRemark("接口响应报文为空");
                                apiParamConfigService.updateApiParamConfig(apiParamConfig);
                                continue;
                            }
                        }
                        String  fileCreatePath=apiParamConfig.getFileCreatePath();
                        String  fileTargetPath=apiParamConfig.getFileTargetPath();
                        String   prefFileName=apiParamConfig.getPrefFileName();
                        String fileName=prefFileName+execDate+ConstantUtil.TXT;
                        log.info("写入文件路径:"+fileCreatePath+prefFileName+execDate+ConstantUtil.TXT);
                        TxtUtil.writeToTxt(data_list,fileCreatePath+fileName);
                        this.removeFile(fileName,fileCreatePath,fileTargetPath);//文件生成后转移至指定目录下
                        setExecDate(cycle,execDate,apiParamConfig);//切换作业日期
                        apiParamConfig.setRemark("执行成功");
                        if(ConstantUtil.DATA_CYCLE.TEMP.getValue().equals(cycle)){
                            apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.SLEEP.getValue());//休眠
                        }else{
                            apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.SUCCESS.getValue());//成功
                        }
                        apiParamConfig.setRemark(reply_msg);
                        apiParamConfigService.updateApiParamConfig(apiParamConfig);
                    }else{
                        //直接把总行服务器上的文件下载下来
                        //String ip,int port,String userName,String password,String sourceFile,String targetFile,String targetFileName
                        //FileUtil.copyRemoteFile();
                    }
                }catch (Exception ex){
                    apiParamConfig.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                    apiParamConfig.setRemark("程序异常:"+ex.toString());
                    apiParamConfigService.updateApiParamConfig(apiParamConfig);
                    continue;
                }


            }
        }
        log.info("====定时任务执行结束（获取API数据,生成TXT文件）======");
    }


    private void  removeFile(String fileName,String sourcePath,String targetPath){
        File file=new File(sourcePath+fileName);
        if(file.exists()){
            String shell="mv "+sourcePath+fileName+" "+targetPath;
            log.info("执行shell脚本:"+shell);
            try {
                Runtime.getRuntime().exec(shell).waitFor();
            } catch (IOException e1) {
                e1.printStackTrace();
            }catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    //切换执行日期
    private void setExecDate(String cycle,String execDate,ApiParamConfig apiParamConfig){
        if(ConstantUtil.DATA_CYCLE.DAY.getValue().equals(cycle)){
            apiParamConfig.setExecDate(DateUtil.addDay(execDate,ConstantUtil.ONE));//执行日期指向下一天
        }else if(ConstantUtil.DATA_CYCLE.MON.getValue().equals(cycle)){
            boolean isLastDayOfMonth=DateUtil.isLastDayOfMonth(execDate);
            if(isLastDayOfMonth){
                apiParamConfig.setExecDate(DateUtil.nextMonthEndDate(execDate));//执行日期指向下月末
            }else {
                apiParamConfig.setExecDate(DateUtil.addMonthDate(execDate)); //执行日期加1个月
            }

        }else if(ConstantUtil.DATA_CYCLE.QUARTER.getValue().equals(cycle)){
            apiParamConfig.setExecDate(DateUtil.nextQuarterEndDate(execDate)); //执行日期指向下个季度末
        }
    }
}
