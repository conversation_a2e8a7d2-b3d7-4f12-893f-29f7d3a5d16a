<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.DictConfigDao" >
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.DictConfig" >
        <id column="id" property="id" jdbcType="DOUBLE" />
        <result column="key_name" property="keyName" jdbcType="VARCHAR" />
        <result column="value_name" property="valueName" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        id,key_name,value_name
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Double" >
        select
        <include refid="Base_Column_List" />
        from
        t_dict_config
        where id =#{id,jdbcType=DOUBLE}
    </select>

    <select id="selectByKeyName" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from
        t_dict_config
        where  key_name =#{keyName,jdbcType=VARCHAR}
    </select>
</mapper>