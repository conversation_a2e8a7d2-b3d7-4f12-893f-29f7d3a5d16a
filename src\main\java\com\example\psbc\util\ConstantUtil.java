package com.example.psbc.util;

/**
 * Created by xmpsb-xiaohj on 2021/11/12.
 */
public class ConstantUtil{

    //public   static final String  STATUS_SUCCESS_CODE="0";//成功
    //public  static final String  STATUS_EXEC_CODE="1";//执行中
    //public  static final String  STATUS_FAIL_CODE="2";//失败
    //public  static final String  STATUS_SLEEP_CODE="9";//休眠（需手工调整为状态0，才可执行，适用于一次性取数）
    public   static  final String RK_SCHEDULE_CYCLE="rk_schedule_cycle";
    public   static  final String TXT_SCHEDULE_CYCLE="txt_schedule_cycle";

    public   static  final String  URL_PI_XXX="PI_XXX";
    public   static  final String  URL_TOTAL_XXX="TOTAL_XXX";
    public   static  final String  PARAM_TX_DATE="TX_DATE_XXX";
    public   static  final int    ONE=1;

    public static  final  String  SEPARATOR="|+|";//分隔符
    public static  final  String  TXT=".txt";
    public static  final  String  UDC="udc";
    public static  final  String  op_key="35008816";
    public static  final  String  code="000000";


    public  enum DATA_CYCLE{
      DAY("D"),//天
      MON("M"),//月
      QUARTER("Q"),//季
      TEMP("TEMP");//临时
     private String name;
    // 构造方法
      private DATA_CYCLE(String name){
          this.name = name;
      }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }



    public  enum DATA_GET_WAY{
        API("API"),
        FILE("FILE");
        private String name;
        // 构造方法
        private DATA_GET_WAY(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }


  //是否删除标志位
    public  enum DEL_FLAG{
        YES("1"),
        NO("0");
        private String name;
        // 构造方法
        private DEL_FLAG(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }




    //是否分页
    public  enum IS_PAGE{
        YES("1"),
        NO("0");
        private String name;
        // 构造方法
        private IS_PAGE(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }

    //是否可为空文件(1:是 0:否)
    public  enum IS_EMPTY_FILE{
        YES("1"),
        NO("0");
        private String name;
        // 构造方法
        private IS_EMPTY_FILE(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }



    //API响应代码
    public  enum API_RES_CODE{
        SUCCESS("0"),
        FAIL("-1");
        private String name;
        // 构造方法
        private API_RES_CODE(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }



    //任务状态
    public  enum TASK_STATUS{
        SUCCESS("0"),//成功
        EXEC("1"),//执行中
        FAIL("2"),//失败
        SLEEP("9");//休眠
        private String name;
        // 构造方法
        private TASK_STATUS(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }

    //数据导入标识
    public  enum IMPORT_FLAG{
        SUCCESS("0"),//成功
        FAIL("1");//失败
        private String name;
        // 构造方法
        private IMPORT_FLAG(String name){
            this.name = name;
        }
        // 覆盖方法
        //@Override
        public String getValue(){
            return this.name;
        }
    }


}
