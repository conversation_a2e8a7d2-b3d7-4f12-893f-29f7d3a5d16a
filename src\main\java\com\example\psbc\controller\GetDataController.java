package com.example.psbc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.example.psbc.entity.ApiParamConfig;
import com.example.psbc.entity.DynamicTable;
import com.example.psbc.entity.KeyParam;
import com.example.psbc.entity.SjjsDataChk;
import com.example.psbc.services.ApiParamConfigService;
import com.example.psbc.services.DynamicTableService;
import com.example.psbc.services.KeyParamService;
import com.example.psbc.services.SjjsDataChkService;
import com.example.psbc.util.*;
import com.sun.corba.se.impl.ior.OldJIDLObjectKeyTemplate;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 肖厚敬 on 2022/3/31.
 */
@RestController
@RequestMapping("/sjjs")
public class GetDataController{
    @Autowired
    private ApiParamConfigService apiParamConfigService;
    @Autowired
    private SjjsDataChkService sjjsDataChkService;
    @Autowired
    private DynamicTableService dynamicTableService;
    @Autowired
    private KeyParamService keyParamService;

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    final  static String  fileCreatePath="/app/cpdds/sjjs/run/";
    @RequestMapping(value = "/getUrlData")
    public  Object  getUrlData(@RequestBody
            @Param("jobDate") String jobDate,@Param("serviceId") String serviceId,@Param("tbName") String tbName,@Param("paramKey") String paramKey){
        String  count_url="https://*************:8084/bds-api/api/bds/countApi?servicecd=CD_XXX";
        String  task_info_url="https://*************:8084/open-api/api/bds/getTaskInfo?servicecd=CD_XXX";
        String  data_url="https://*************:8084/bds-api/api/bds/extractApi?servicecd=CD_XXX&total=TOTAL_XXX&pageIndex=PI_XXX&pageSize=1000";
        ApiParamConfig apiParamConfig=apiParamConfigService.selectByTbName(tbName);
        System.out.println("=======================================================");
        System.out.println("jobDate="+jobDate);
        System.out.println("serviceId="+serviceId);
        System.out.println("tbName="+tbName);
        System.out.println("=======================================================");
        HashMap<String, Object> resMap=new HashMap<>();
        HashMap<String, Object> paramMap = new HashMap<>();
        String jobDate2="'"+jobDate.substring(0,4)+"-"+jobDate.substring(4,6)+"-"+jobDate.substring(6)+"'";
        paramMap.put(ConstantUtil.UDC,apiParamConfig.getReqParam()+jobDate2);
        count_url=count_url.replace("CD_XXX",serviceId);
        JSONObject countJsonObject= CallInterfaceUtil.postUrlData(count_url,paramMap);
        JSONArray jsonArray=(JSONArray)countJsonObject.get("data");
        System.out.println("=======================================================");
        System.out.println(ConstantUtil.UDC+"="+apiParamConfig.getReqParam()+jobDate2);
        System.out.println("count_url="+count_url);
        System.out.println("jsonArray="+jsonArray.toJSONString());
        System.out.println("jsonArray的长度="+jsonArray.size());
        System.out.println("=======================================================");
        String  reply_msg="";
        int     errorIndex=0;
        boolean isException=false;
        String  url="";
        int  index=0;
        if(jsonArray!=null && jsonArray.size()>0){
            int total=(int)jsonArray.getJSONObject(0).get("total");
            index=total % 1000==0?(total/1000):(total/1000)+1;//循环次数（即页数）
            List<String> data_list=new ArrayList();//用来封装数据
            for(int j=1;j<=index;j++){
                String totalStr=total+"";
                String pageIndex=j+"";
                //替换总数量占位符&替换页码占位符&替换接口代码
                url=data_url.replace(ConstantUtil.URL_TOTAL_XXX,totalStr)
                            .replace(ConstantUtil.URL_PI_XXX,pageIndex)
                            .replace("CD_XXX",serviceId);
                //System.out.println("=======================================================");
                System.out.println("url="+url);
                JSONObject resJsonObject=CallInterfaceUtil.postUrlData(url,paramMap);//调用接口获取数据列表
                //System.out.println("resJsonObject="+resJsonObject.toJSONString());
                System.out.println("=======================================================");
                reply_msg= TypeConversionUtil.jsonToList(resJsonObject,data_list);//将数据封装成LIST
                if(!reply_msg.equals("ok")){
                    errorIndex=j;
                    isException=true;
                    break;
                }
            }
            TxtUtil.writeToTxt(data_list,fileCreatePath+tbName+"_"+jobDate+ConstantUtil.TXT);
        }
        if(isException){
            resMap.put("errorIndex","第"+errorIndex+"页");
            resMap.put("errorMsg",reply_msg);
            return  resMap.toString();
        }
        resMap.put("结果","ok");
        resMap.put("count_url",count_url);
        resMap.put("jsonArray",jsonArray.toJSONString());
        resMap.put("循环次数",index);
        resMap.put("url",url);
        System.out.println("=================开始入库======================================");
        importData(tbName,jobDate,"6");
        return resMap;
    }
    //将txt文件加载到数据库
    @RequestMapping("/importData")
    public  Object  importData(@Param("fileId") String fileId,@Param("fileDate") String fileDate,@Param("procType") String procType){
        String fileName=fileId+"_"+fileDate+ConstantUtil.TXT;
        File file=new File(fileName);
        if(file.exists()){
            try {
                SjjsDataChk sjjsDataChk=sjjsDataChkService.selectByPrimaryKey(fileId);
                if(ConstantUtil.DEL_FLAG.YES.getValue().equals(sjjsDataChk.getDelFlag())){
                    DynamicTable dynamicTable=new DynamicTable();
                    dynamicTable.setTableName(fileId);
                    dynamicTable.setDateKey(sjjsDataChk.getDateKey());
                    dynamicTable.setDateValue(fileDate);
                    dynamicTableService.delTableDataByDate(dynamicTable);
                }
                String  param=" "+fileName+" "+sjjsDataChk.getProcType();
                SheelUtil.execSh("/app/cpdds/sjjs/bat/loadsjjs.sh",param);
            }catch (Exception e1){
                return e1.getMessage();
            }
        }else{
           return "txt文件未生成";
        }
        return "ok";
    }
    @RequestMapping("/id")
    public Object updateByKeyParam(@Param("id") String id,@Param("opkey") String opkey,@Param("opsta") String opsta){
        try {
        KeyParam keyParam=new KeyParam();
        keyParam.setId(id);
        keyParam.setOpKey(opkey);
        keyParam.setOpSta(opsta);
        keyParamService.upById(keyParam);
        }catch (Exception e1){
            return e1.getMessage();
        }
       return "ok";
    }


}
