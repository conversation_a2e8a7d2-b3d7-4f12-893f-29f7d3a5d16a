package com.example.psbc.controller;

import com.example.psbc.entity.DynamicTable;
import com.example.psbc.entity.SjjsDataChk;
import com.example.psbc.entity.SjjsImportDataResult;
import com.example.psbc.entity.SjjsTargetConfig;
import com.example.psbc.services.*;
import com.example.psbc.util.ConstantUtil;
import com.example.psbc.util.DateUtil;
import com.example.psbc.util.SheelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by xhj on 2022/5/23.
 * 将存于本地服务器指定目录下的txt文件解析入库
 */
@Configuration
public class ImportDataTask{
    @Autowired
    private SjjsDataChkService sjjsDataChkService;
    @Autowired
    private DynamicTableService dynamicTableService;

    @Autowired
    private PostgresCopyService postgresCopyService;
    @Autowired
    private SjjsTargetConfigService sjjsTargetConfigService;
    @Autowired
    private SjjsImportDataResultService sjjsImportDataResultService;


    @Value("${pg.delimiter}")
    private String delimiter;


    private final Logger log = LoggerFactory.getLogger(this.getClass());
    public void dealTask(){
        log.info("====定时任务开始执行（解析TXT文件入库）======");
        System.setProperty("jdk.tls.allowUnsafeServerCertChange","true");
        System.setProperty("sun.security.ssl.allowUnsafeRenegotiation","true");
        System.setProperty("jdk.tls.client.protocols","TLSv1,TLSv1.1,TLSv1.2");
        List<SjjsDataChk> list=sjjsDataChkService.selectExecTaskList();
        if(list!=null && list.size()>0){
            for (int i=0;i<list.size();i++){
                SjjsDataChk  sjjsDataChk=list.get(i);
                String fileDate=sjjsDataChk.getFileDate();
                //处理线程并发问题
                synchronized (SjjsDataChk.class){
                    String  fileId=sjjsDataChk.getFileId();
                    SjjsDataChk   nowObject=sjjsDataChkService.selectByPrimaryKey(fileId);
                    String nowStatus=nowObject.getStatus();
                    String nowFileDate=nowObject.getFileDate();
                    if(ConstantUtil.TASK_STATUS.EXEC.getValue().equals(nowStatus) || !nowFileDate.equals(fileDate)){
                        log.info("其他线程正常处理该文件:"+sjjsDataChk.getFileHead()+sjjsDataChk.getFileDate()+sjjsDataChk.getFileTail());
                        continue;
                    }else{
                        sjjsDataChk.setStatus(ConstantUtil.TASK_STATUS.EXEC.getValue());
                        sjjsDataChk.setRemark("执行中");
                        sjjsDataChkService.updateSjjsDataChk(sjjsDataChk);
                    }
                }
                if(DateUtil.isGtToday(fileDate)){
                    sjjsDataChk.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                    sjjsDataChk.setRemark("执行日期大于当前日期");
                    sjjsDataChkService.updateSjjsDataChk(sjjsDataChk);
                    continue;
                }
                String fileId=sjjsDataChk.getFileId();//表名
                String filePath=sjjsDataChk.getFilePath();
                String fileHead=sjjsDataChk.getFileHead();
                String fileTail=sjjsDataChk.getFileTail();
                String procName=sjjsDataChk.getProcName();
                String procType=sjjsDataChk.getProcType();
                String dateType=sjjsDataChk.getDateType();
                String dateKey=sjjsDataChk.getDateKey();
                String delFlag=sjjsDataChk.getDelFlag();

                String param1=fileHead+fileDate+fileTail;
                String param2=procType;
                String sh=procName;
                String  param=" "+param1+" "+param2;
                File file=new File(filePath+fileHead+fileDate+fileTail);
                log.info("入库文件名:"+filePath+fileHead+fileDate+fileTail);
                if(file.exists()){
                    try {
                        if(ConstantUtil.DEL_FLAG.YES.getValue().equals(delFlag)){
                            log.info("=====开始删除数据========");
                            DynamicTable dynamicTable=new DynamicTable();
                            dynamicTable.setTableName(fileId);
                            dynamicTable.setDateKey(dateKey);
                            dynamicTable.setDateValue(fileDate);
                            dynamicTableService.delTableDataByDate(dynamicTable);
                            log.info("=====结束删除数据========");
                        }
                        //SheelUtil.execSh(sh,param);//数据入库
                        SjjsTargetConfig sjjsTargetConfig=new SjjsTargetConfig();
                        sjjsTargetConfig.setStatus("1");
                        sjjsTargetConfig.setTbNameEn(fileId);
                        List<SjjsTargetConfig> sjjsTargetConfigList=sjjsTargetConfigService.selectListByParam(sjjsTargetConfig);
                        if (sjjsTargetConfigList == null || sjjsTargetConfigList.isEmpty()) {
                            throw new IllegalArgumentException(fileId+"表结构不能为空");
                        }
                        String columnsStr = sjjsTargetConfigList.stream()
                                .map(SjjsTargetConfig::getColumnNameEn)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(","));
                        postgresCopyService.importTxt(filePath+fileHead+fileDate+fileTail,fileId,columnsStr,delimiter,false);
                    }catch (Exception e1){
                        sjjsDataChk.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                        sjjsDataChk.setRemark("执行异常,请查看日志");
                        sjjsDataChkService.updateSjjsDataChk(sjjsDataChk);
                        SjjsImportDataResult sjjsImportDataResult=new SjjsImportDataResult();
                        sjjsImportDataResult.setTbNameEn(fileId);
                        sjjsImportDataResult.setFlag(ConstantUtil.IMPORT_FLAG.FAIL.getValue());
                        sjjsImportDataResult.setResultMsg(e1.getMessage());
                        sjjsImportDataResult.setCauseMsg(e1.getCause()==null?"":e1.getCause().getMessage());
                        sjjsImportDataResultService.insertSjjsImportDataResult(sjjsImportDataResult);
                        e1.printStackTrace();
                        log.error(e1.getMessage());
                        continue;
                    }
                    sjjsDataChk.setStatus(ConstantUtil.TASK_STATUS.SUCCESS.getValue());
                    sjjsDataChk.setRemark("执行成功");
                    if(ConstantUtil.DATA_CYCLE.DAY.getValue().equals(dateType)){
                        sjjsDataChk.setFileDate(DateUtil.addDay(fileDate,1));
                    }else if (ConstantUtil.DATA_CYCLE.MON.getValue().equals(dateType)){
                        boolean isLastDayOfMonth=DateUtil.isLastDayOfMonth(fileDate);
                        if(isLastDayOfMonth){
                            sjjsDataChk.setFileDate(DateUtil.nextMonthEndDate(fileDate));
                        }else{
                            sjjsDataChk.setFileDate(DateUtil.addMonthDate(fileDate)); //执行日期加1个月
                        }
                    }else if(ConstantUtil.DATA_CYCLE.QUARTER.getValue().equals(dateType)){
                        sjjsDataChk.setFileDate(DateUtil.nextQuarterEndDate(fileDate));
                    }else if(ConstantUtil.DATA_CYCLE.TEMP.getValue().equals(dateType)){
                        sjjsDataChk.setStatus(ConstantUtil.TASK_STATUS.SLEEP.getValue());//休眠
                    }
                    sjjsDataChkService.updateSjjsDataChk(sjjsDataChk);
                }else{
                    sjjsDataChk.setStatus(ConstantUtil.TASK_STATUS.FAIL.getValue());
                    sjjsDataChk.setRemark("txt文件未生成");
                    sjjsDataChkService.updateSjjsDataChk(sjjsDataChk);
                    log.info("==="+fileHead+fileDate+fileTail+"===文件未生成===");
                    System.out.println("==="+fileHead+fileDate+fileTail+"===文件未生成===");
                }
            }
        }
        //System.out.println("====定时任务执行结束（解析TXT文件入库）======");
        log.info("====定时任务执行结束（解析TXT文件入库）======");
    }

}
