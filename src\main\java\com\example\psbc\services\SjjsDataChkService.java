package com.example.psbc.services;

import com.example.psbc.entity.SjjsDataChk;

import java.util.List;

/**
 * Created by xmpsb-xiaohj on 2021/11/11.
 */
public interface SjjsDataChkService {

    SjjsDataChk selectByPrimaryKey(String fileId);
    List<SjjsDataChk> selectExecTaskList();
    List<SjjsDataChk> selectListByParam(SjjsDataChk sjjsDataChk);
    void  updateSjjsDataChk(SjjsDataChk sjjsDataChk);
}
