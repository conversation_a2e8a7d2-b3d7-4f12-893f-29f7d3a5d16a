#
server:
  port: 8989
spring:
   profiles:
        active: dev   #指向(开发为dev，测试为test，生产为prod)
   mvc:
      async:
        request-timeout: 1800000
  #datasource:
  #username: system
  #password: orcl
  #url: *************************************
  #username: cpddp_pdata
  #password: sjxz3721DATCPOU
  #url: *********************************************
  #username: psbc_xm
  #password: xiamen10
  #driver-class-name: oracle.jdbc.driver.OracleDriver

#mybatis
mybatis:
  type-aliases-package: com.example.psbc.entity
  mapper-locations: classpath:mapper/*.xml

logging:
    config: classpath:log/logback-spring.xml

#线程池配置
#请求接口数据线程池
getData:
  corePoolSize: 3       #核心线程数
  queueSize: 5
  maximumPoolSize: 5
  keepAliveTime: 120
  threadNum: 3          #同时启动的线程数

#导入数据线程池
importData:
  corePoolSize: 3      #核心线程数
  queueSize: 5
  maximumPoolSize: 5
  keepAliveTime: 120
  threadNum: 3         #同时启动的线程数
pg:
  delimiter: "`"         #PG COPY 的分隔符
---
spring:
    profiles: dev   #开发环境
    datasource:
             url: *************************************
             driver-class-name: org.postgresql.Driver
             username: postgres
             password: abc123..

---
spring:
    profiles: test   #测试环境
    datasource:
             username: psbc_kf
             password: Psdx.1378!
             url: ****************************************
             driver-class-name: oracle.jdbc.driver.OracleDriver
---
spring:
   profiles: prod  #生产环境
   datasource:
             username: cpddp_pdata
             password: sjxz3721DATCPOU
             url: *********************************************
             driver-class-name: oracle.jdbc.driver.OracleDriver