package com.example.psbc.dao;

import com.example.psbc.entity.ApiParamConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Vector;

/**
 * Created by xmpsb-xiaohj on 2021/10/22.
 */
public interface  ApiParamConfigDao{
    ApiParamConfig selectByPrimaryKey(Double id);
    ApiParamConfig selectByTbName(String tbName);
    Vector<ApiParamConfig> selectExecTaskList();
    Vector<ApiParamConfig> selectListByParam(ApiParamConfig apiParamConfig);
    void  updateApiParamConfig(ApiParamConfig apiParamConfig);
}
