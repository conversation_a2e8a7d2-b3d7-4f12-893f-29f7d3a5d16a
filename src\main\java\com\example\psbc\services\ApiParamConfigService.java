package com.example.psbc.services;

import com.example.psbc.entity.ApiParamConfig;

import java.util.List;
import java.util.Vector;

/**
 * Created by xmpsb-xiaohj on 2021/10/22.
 */
public interface ApiParamConfigService {

    ApiParamConfig selectByPrimaryKey(Double id);
    ApiParamConfig selectByTbName(String tbName);
    Vector<ApiParamConfig> selectListByParam(ApiParamConfig apiParamConfig);
    Vector<ApiParamConfig> selectExecTaskList();
    void  updateApiParamConfig(ApiParamConfig apiParamConfig);
}
