package com.example.psbc.services.impl;

import com.example.psbc.dao.SjjsImportDataResultDao;
import com.example.psbc.entity.SjjsImportDataResult;
import com.example.psbc.services.SjjsImportDataResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("sjjsImportDataResultService")
public class SjjsImportDataResultServiceImpl implements SjjsImportDataResultService {
    @Autowired
    private SjjsImportDataResultDao sjjsImportDataResultDao;
    @Override
    public int insertSjjsImportDataResult(SjjsImportDataResult sjjsImportDataResult) {
        return sjjsImportDataResultDao.insertSjjsImportDataResult(sjjsImportDataResult);
    }
}
