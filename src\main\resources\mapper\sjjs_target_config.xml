<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.SjjsTargetConfigDao">
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.SjjsTargetConfig" >
        <result column="TB_NAME_EN" property="tbNameEn" jdbcType="VARCHAR" />
        <result column="TB_NAME_CN" property="tbNameCn" jdbcType="VARCHAR"/>
        <result column="COLUMN_NAME_EN" property="columnNameEn" jdbcType="VARCHAR"/>
        <result column="COLUMN_NAME_CN" property="columnNameCn" jdbcType="VARCHAR" />
        <result column="COLUMN_TYPE" property="columnType" jdbcType="VARCHAR" />
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="COLUMN_ORDER" property="columnOrder" jdbcType="INTEGER"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="INTEGER"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        TB_NAME_EN,TB_NAME_CN,COLUMN_NAME_EN,COLUMN_NAME_CN,
        COLUMN_TYPE,STATUS,COLUMN_ORDER,
        CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,REMARK
    </sql>


    <select id="selectListByParam" resultMap="BaseResultMap" parameterType="com.example.psbc.entity.SjjsTargetConfig">
        select
        <include refid="Base_Column_List"/>
        from T_SJJS_TARGET_CONFIG
        where 1=1
        <if test="status != null">
            and STATUS=#{status}
        </if>
        <if test="tbNameEn != null">
            and TB_NAME_EN=#{tbNameEn}
        </if>
        order by COLUMN_ORDER
    </select>



</mapper>