package com.example.psbc.util;

import com.example.psbc.entity.MyException;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * Created by test on 2022/7/13.
 */
public class SheelUtil {
    public static void execSh(String sh,String param){
        //final Process process = null;
        String command =  "sh " + sh+param;
        //log.info("执行shell脚本:"+command);
        System.out.println("执行shell脚本:"+command);
        try {
            final Process process=Runtime.getRuntime().exec(command);
            //必须等待该进程结束，否则时间设置就无法生效

            //处理InputStream的线程
            new Thread()
            {
                @Override
                public void run()
                {
                    BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
                    String line = null;

                    try
                    {
                        while((line = in.readLine()) != null)
                        {
                            System.out.println("output: " + line);
                        }
                    }
                    catch (IOException e)
                    {
                        e.printStackTrace();
                    }
                    finally
                    {
                        try
                        {
                            in.close();
                        }
                        catch (IOException e)
                        {
                            e.printStackTrace();
                        }
                    }
                }
            }.start();

            //处理ErrorStream的线程
            new Thread()
            {
                @Override
                public void run()
                {
                    BufferedReader err = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                    String line = null;

                    try
                    {
                        while((line = err.readLine()) != null)
                        {
                            System.out.println("err: " + line);
                        }
                    }
                    catch (IOException e)
                    {
                        e.printStackTrace();
                    }
                    finally
                    {
                        try
                        {
                            err.close();
                        }
                        catch (IOException e)
                        {
                            e.printStackTrace();
                        }
                    }
                }
            }.start();

            process.waitFor();
        } catch (IOException ex){
            throw new MyException("脚本执行异常1:===="+ex.getMessage());
        }catch (InterruptedException ex){
            throw new MyException("脚本执行异常2:===="+ex.getMessage());
        }catch (Exception ex){
            throw new MyException("脚本执行异常3:===="+ex.getMessage());
        }

    }
}
