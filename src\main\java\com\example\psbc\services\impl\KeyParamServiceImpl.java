package com.example.psbc.services.impl;


import com.example.psbc.dao.KeyParamDao;
import com.example.psbc.entity.KeyParam;
import com.example.psbc.services.KeyParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("KeyParamService")
public class KeyParamServiceImpl implements KeyParamService {
  @Autowired
  private KeyParamDao keyParamDao;

    @Override
    public KeyParam selectGk() {
        return keyParamDao.selectGetKey();
    }
  @Override
    public void upById(KeyParam keyParam){
      keyParamDao.upById(keyParam);
    }
}
