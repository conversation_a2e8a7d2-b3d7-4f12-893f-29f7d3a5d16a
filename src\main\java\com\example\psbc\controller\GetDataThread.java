package com.example.psbc.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.example.psbc.entity.ApiParamConfig;
import com.example.psbc.services.ApiParamConfigService;
import com.example.psbc.util.CallInterfaceUtil;
import com.example.psbc.util.ConstantUtil;
import com.example.psbc.util.DateUtil;
import com.example.psbc.util.TxtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import java.io.File;
import java.io.IOException;
import java.util.*;


/**
 * Created by xhj on 2021/11/5.
 *描述:从数据集市上通过API获取数据，并生成txt文件
 */
//@Configuration
public class GetDataThread implements Runnable{

    private GetDataTask getDataTask;

    public GetDataThread (GetDataTask getDataTask){
        this.getDataTask=getDataTask;
    }
    @Override
    public void run(){
        getDataTask.dealTask();
    }



    //end
}
