package com.example.psbc.services.impl;

import com.example.psbc.dao.ApiParamConfigDao;
import com.example.psbc.entity.ApiParamConfig;
import com.example.psbc.services.ApiParamConfigService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import javax.annotation.Resource;
import java.util.List;
import java.util.Vector;

/**
 * Created by xmpsb-xiaohj on 2021/10/22.
 */
@Service("apiParamConfigService")
public class ApiParamConfigServiceImpl  implements  ApiParamConfigService{
    @Autowired
    private ApiParamConfigDao apiParamConfigDao;

    @Override
    public ApiParamConfig selectByPrimaryKey(Double id) {
        return apiParamConfigDao.selectByPrimaryKey(id);
    }

    @Override
    public ApiParamConfig selectByTbName(String tbName) {
        return apiParamConfigDao.selectByTbName(tbName);
    }


    @Override
    public Vector<ApiParamConfig> selectListByParam(ApiParamConfig apiParamConfig) {
        return apiParamConfigDao.selectListByParam(apiParamConfig);
    }

    @Override
    public Vector<ApiParamConfig> selectExecTaskList(){
        return apiParamConfigDao.selectExecTaskList();
    }

    @Override
    public void updateApiParamConfig(ApiParamConfig apiParamConfig){
        apiParamConfigDao.updateApiParamConfig(apiParamConfig);
    }
}
