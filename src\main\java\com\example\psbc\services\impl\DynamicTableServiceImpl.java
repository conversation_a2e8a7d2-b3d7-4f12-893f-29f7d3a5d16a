package com.example.psbc.services.impl;

import com.example.psbc.dao.DynamicTableDao;
import com.example.psbc.entity.DynamicTable;
import com.example.psbc.services.DynamicTableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by xmpsb-xiaohj on 2021/11/18.
 */
@Service("dynamicTableService")
public class DynamicTableServiceImpl implements DynamicTableService{

    @Autowired
    private DynamicTableDao dynamicTableDao;

    @Override
    public void delTableDataByDate(DynamicTable dynamicTable){
        dynamicTableDao.delTableDataByDate(dynamicTable);
    }
}
