package com.example.psbc.util;

import com.example.psbc.entity.MyException;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.List;

/**
 * Created by xiaohj on 2021/11/4.
 */
public class TxtUtil {

public   static   void  writeToTxt(List list,String path){
    FileOutputStream outStr=null;
    BufferedOutputStream  buff=null;
    String enter="\r\n";
    StringBuffer write;
    try {
        outStr=new FileOutputStream(new File(path));
        buff=new BufferedOutputStream(outStr);
        for(int i=0;i<list.size();i++){
             write=new StringBuffer();
             write.append(list.get(i));
             write.append(enter);
             buff.write(write.toString().getBytes("GBK"));//UTF-8
        }
        buff.flush();
        buff.close();
    }catch (Exception ex){
        ex.printStackTrace();
        throw new MyException("文件写入异常===="+ex.getMessage());
    }finally{
        try{
            buff.flush();
            buff.close();
        }catch (Exception ex){
            ex.printStackTrace();
        }
        //throw new Exception("这是手动抛出异常！");
    }
}



}
