package com.example.psbc.controller;

import com.example.psbc.entity.DynamicTable;
import com.example.psbc.entity.MyException;
import com.example.psbc.entity.SjjsDataChk;
import com.example.psbc.services.DynamicTableService;
import com.example.psbc.services.SjjsDataChkService;
import com.example.psbc.util.ConstantUtil;
import com.example.psbc.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

/**
 * Created by 肖厚敬 on 2021/11/11.
 * 描述:将txt文件解析入库
 */
//@Configuration
public class ImportDataThread  implements Runnable{

private ImportDataTask importDataTask;

public ImportDataThread(ImportDataTask importDataTask){
    this.importDataTask=importDataTask;
}

@Override
public void run(){
    importDataTask.dealTask();
}




}
