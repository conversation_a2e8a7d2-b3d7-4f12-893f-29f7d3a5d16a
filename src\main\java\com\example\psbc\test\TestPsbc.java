package com.example.psbc.test;


import java.io.UnsupportedEncodingException;
import java.security.Security;
import java.util.Arrays;

import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

public class TestPsbc
{
  private static final String ENCODING = "UTF-8";
  
  static
  {
    Security.addProvider(new BouncyCastleProvider());
  }
  
  public static String encrypt(String paramStr)
  {
    String resultHexString = "";
    try
    {
      byte[] srcData = paramStr.getBytes("UTF-8");
      
      byte[] resultHash = hash(srcData);
      
      resultHexString = ByteUtils.toHexString(resultHash);
    }
    catch (UnsupportedEncodingException e)
    {
      e.printStackTrace();
    }
    return resultHexString;
  }
  
  public static byte[] hash(byte[] srcData)
  {
    SM3Digest digest = new SM3Digest();
    digest.update(srcData, 0, srcData.length);
    byte[] hash = new byte[digest.getDigestSize()];
    digest.doFinal(hash, 0);
    return hash;
  }
  
  public static boolean verify(String srcStr, String sm3HexString)
  {
    boolean flag = false;
    try
    {
      byte[] srcData = srcStr.getBytes("UTF-8");
      byte[] sm3Hash = ByteUtils.fromHexString(sm3HexString);
      byte[] newHash = hash(srcData);
      if (Arrays.equals(newHash, sm3Hash)) {
        flag = true;
      }
    }
    catch (UnsupportedEncodingException e)
    {
      e.printStackTrace();
    }
    return flag;
  }
  
  public static String psbcEncrypt(String srcStr)
  {
    if (srcStr != null)
    {
      if ("".equals(srcStr)) {
        return "";
      }
      if (srcStr.matches("^[0-9]*$"))
      {
        long num = Long.parseLong(srcStr);
        num += 35008816L;
        return encrypt(num+"");
      }
      return encrypt("PSBC" + srcStr);
    }
    return null;
  }
  public static void main(String[] args) {
		
	}
  
}
