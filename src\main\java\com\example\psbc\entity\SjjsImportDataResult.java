package com.example.psbc.entity;

import java.time.LocalDateTime;

public class SjjsImportDataResult {
    private String tbNameEn;
    private LocalDateTime createTime;
    private String flag;
    private String resultMsg;
    private String causeMsg;

    public String getTbNameEn() {
        return tbNameEn;
    }

    public void setTbNameEn(String tbNameEn) {
        this.tbNameEn = tbNameEn;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getCauseMsg() {
        return causeMsg;
    }

    public void setCauseMsg(String causeMsg) {
        this.causeMsg = causeMsg;
    }
}
