package com.example.psbc.test;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.example.psbc.util.ConstantUtil;
import com.example.psbc.util.DateUtil;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Scanner;

/**
 * Created by xmpsb-xiaohj on 2021/11/1.
 */
public class Test {
   private  final    int e=10; //常量
   private  String   qw; //成员变量
   private  static String q;//静态变量

    public static void main(String[] args){
        String  d=DateUtil.addMonthDate("20230405");
        String  d1=DateUtil.addMonthDate("20230505");
        String  d2=DateUtil.addMonthDate("20230605");
        System.out.println(d);
        System.out.println(d1);
        System.out.println(d2);
        /*JSONObject countJsonObject=new JSONObject();
        countJsonObject.put("code","-1");
        countJsonObject.put("message","openAPI服务执行异常");
        System.out.print(countJsonObject.get("code"));*/


    }

    public static String getMD5Str(String str) {


        byte[] digest = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            digest  = md5.digest(str.getBytes("utf-8"));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //16是表示转换为16进制数
        String md5Str = new BigInteger(1, digest).toString(16);
        return md5Str;
    }


    public static String getSM3Str(String str) {
        String  res="";
        try{
            byte[] srcData=str.getBytes("utf-8");
             byte[] resHash=hash(srcData);
             res= ByteUtils.toHexString(resHash);
        }catch (Exception ex){

        }

        return res;
    }
    public  static byte[] hash(byte[] srcData){
        SM3Digest digest=new  SM3Digest();
        digest.update(srcData,0,srcData.length);
        byte[] hash=new byte[digest.getDigestSize()];
        digest.doFinal(hash,0);
        return hash;
    }

}



