<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.LoanDtlDao" >
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.LoanDtl" >
        <result column="txn_date" property="txnDate" jdbcType="VARCHAR" />
        <result column="loan_amt" property="loanAmt" jdbcType="NUMERIC" />
        <result column="cust_name" property="custName" jdbcType="VARCHAR" />
        <result column="cust_no" property="custNo" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        txn_date,loan_amt,cust_name,cust_no
    </sql>
    <select id="getLoanDtlByTxnDate" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select
        <include refid="Base_Column_List" />
        from T_LOAN_DTL
        where txn_date =#{txnDate,jdbcType=VARCHAR}
    </select>
</mapper>