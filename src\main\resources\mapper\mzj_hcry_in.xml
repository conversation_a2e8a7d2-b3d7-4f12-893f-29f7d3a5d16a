<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.MzjHcryInDao" >
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.MzjHcryIn" >
        <id column="pc" property="pc" jdbcType="VARCHAR" />
        <result column="xh" property="xh" jdbcType="VARCHAR" />
        <result column="hdbh" property="hdbh" jdbcType="VARCHAR" />
        <result column="xm" property="xm" jdbcType="VARCHAR" />
        <result column="xfzh" property="xfzh" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        pc,xh,hdbh,xm,xfzh
    </sql>
<select id="selectListByParam" resultMap="BaseResultMap" parameterType="com.example.psbc.entity.MzjHcryIn">
        select
        <include refid="Base_Column_List"/>
        from MZJ_HCRY_IN
        where 1=1
        <if test="pc != null">
            and pc=#{pc}
        </if>
        <if test="hdbh != null">
            and hdbh=#{hdbh}
        </if>
        <if test="xfzh != null">
            and xfzh=#{xfzh}
        </if>
        order by xh
</select>

</mapper>