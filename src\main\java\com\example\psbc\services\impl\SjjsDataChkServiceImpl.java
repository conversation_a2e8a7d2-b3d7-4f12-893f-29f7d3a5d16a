package com.example.psbc.services.impl;

import com.example.psbc.dao.SjjsDataChkDao;
import com.example.psbc.entity.SjjsDataChk;
import com.example.psbc.services.SjjsDataChkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by  on 2021/11/11.
 */
@Service("sjjsDataChkService")
public class SjjsDataChkServiceImpl implements SjjsDataChkService{
    @Autowired
    private SjjsDataChkDao sjjsDataChkDao;

    @Override
    public SjjsDataChk selectByPrimaryKey(String fileId) {
        return sjjsDataChkDao.selectByPrimaryKey(fileId);
    }

    @Override
    public List<SjjsDataChk> selectExecTaskList(){
        return sjjsDataChkDao.selectExecTaskList();
    }

    @Override
    public List<SjjsDataChk> selectListByParam(SjjsDataChk sjjsDataChk) {
        return sjjsDataChkDao.selectListByParam(sjjsDataChk);
    }

    @Override
    public void updateSjjsDataChk(SjjsDataChk sjjsDataChk) {
        sjjsDataChkDao.updateSjjsDataChk(sjjsDataChk);
    }
}
