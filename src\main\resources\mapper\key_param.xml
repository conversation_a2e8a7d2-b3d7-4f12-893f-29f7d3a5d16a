<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.KeyParamDao" >
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.KeyParam" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="op_Key" property="opKey" jdbcType="VARCHAR" />
        <result column="op_sta" property="opSta" jdbcType="VARCHAR" />
    </resultMap>
    <!--parameterType="java.lang.String" -->
    <sql id="Base_Column_List">
        op_key,op_sta
    </sql>
    <select id="selectGetKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        key_param

    </select>
    <update id="upById" parameterType="com.example.psbc.entity.KeyParam">
        update key_param set
        <if test="opKey != null">
            op_key=#{opKey},
        </if>
        <if test="opSta != null">
            op_sta=#{opSta}
        </if>
        where
        id=#{id}
    </update>
</mapper>
