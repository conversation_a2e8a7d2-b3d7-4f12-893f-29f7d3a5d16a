package com.example.psbc.services.impl;

import com.example.psbc.dao.DictConfigDao;
import com.example.psbc.entity.DictConfig;
import com.example.psbc.services.DictConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by xmpsb-xiaohj on 2021/11/4.
 */
@Service("dictConfigService")
public class DictConfigServiceImpl implements DictConfigService{
    @Autowired
    private DictConfigDao dictConfigDao;


    @Override
    public DictConfig selectByPrimaryKey(Double id){
        return dictConfigDao.selectByPrimaryKey(id);
    }

    @Override
    public DictConfig selectByKeyName(String keyName){
        return dictConfigDao.selectByKeyName(keyName) ;

    }
}
