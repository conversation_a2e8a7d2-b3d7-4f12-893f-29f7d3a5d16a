package com.example.psbc.test;

/**
 * Created by xmpsb-xiaohj on 2022/4/22.
 */
public class Thread01   implements  Runnable{
    private  Thread  otherThread;
    Thread01(Thread t){
        otherThread=t;
    }
    @Override
    public void run(){
        try{
        otherThread.join();
        System.out.println("线程Thread01开始执行，线程名字:"+Thread.currentThread().getName());

        Thread.sleep(10*1000);//休眠10秒
        }catch (Exception ex){
            ex.printStackTrace();
        }
        System.out.println("线程Thread01结束，线程名字:"+Thread.currentThread().getName());

    }
}
