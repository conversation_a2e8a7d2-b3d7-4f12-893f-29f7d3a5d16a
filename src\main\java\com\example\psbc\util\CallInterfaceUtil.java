package com.example.psbc.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * Created by xiaohj on 2022/2/15.
 */
public class CallInterfaceUtil {
    public static JSONObject postUrlData(String url,HashMap<String, Object> paramMap) {
        String postBody = JSONObject.toJSONString(paramMap);
        String res = HttpRequest.post(url)
                .header("Content-Type", "application/json;charset=UTF-8")
                .body(postBody)
                .execute().body();
        //System.out.println("接口响应报文:"+res);
        //log.info("接口响应报文:"+res);
        //JSONArray jsonArray= JSONArray.parseArray(res);
        JSONObject  resJsonObject=(JSONObject)JSONObject.parse(res, Feature.OrderedField);////解决转换后顺序不一致问题
        return  resJsonObject;

    }

}
