package com.example.psbc.config;

import com.example.psbc.controller.GetDataTask;
import com.example.psbc.controller.GetDataThread;
import com.example.psbc.services.DictConfigService;
import com.example.psbc.util.ConstantUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;

@Configuration
@EnableScheduling
public class GetDataScheduleConfig implements SchedulingConfigurer {

    @Autowired
    private DictConfigService dictConfigService;

    @Autowired
    private GetDataTask getDataTask;

    @Autowired
    private Executor setExecutor;

    @Autowired
    @Qualifier("getDataTaskExecutor")
    private ExecutorService executor;

    private final Logger log = LoggerFactory.getLogger(GetDataScheduleConfig.class);

    @Value("${getData.threadNum}")
    private int threadNum = 3;

    @Transactional
    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        Runnable task = () -> {
            log.info("=== 开始执行获取数据任务，线程ID：{}", Thread.currentThread().getId());
            //taskScheduler.schedule(new GetDataThread(getDataTask), new Date());
            // 提交 3 个并发子任务
            log.info("=== 主任务开始执行，线程ID：{}", Thread.currentThread().getId());

            // 提交 3 个并发子任务
            for (int i = 0; i < threadNum; i++) {
                int taskId = i;
                try {
                    executor.submit(() -> {
                        try {
                            log.info("=== 子任务 {} 开始执行，线程ID：{}", taskId, Thread.currentThread().getId());
                            new GetDataThread(getDataTask).run(); // 执行实际业务逻辑
                        } catch (Exception e) {
                            log.error("子任务 {} 执行异常", taskId, e);
                        }
                    });
                } catch (Exception e) {
                    log.error("提交子任务 {} 时发生异常", taskId, e);
                }
            }
        };

        Trigger trigger = triggerContext -> {
            String taskCycle = dictConfigService.selectByKeyName(ConstantUtil.TXT_SCHEDULE_CYCLE).getValueName();
            return new CronTrigger(taskCycle).nextExecutionTime(triggerContext);
        };

        scheduledTaskRegistrar.setScheduler(executor);
        scheduledTaskRegistrar.addTriggerTask(task, trigger);
    }
}
