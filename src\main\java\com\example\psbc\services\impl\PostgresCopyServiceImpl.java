package com.example.psbc.services.impl;

import com.example.psbc.entity.SjjsImportDataResult;
import com.example.psbc.services.PostgresCopyService;
import com.example.psbc.services.SjjsImportDataResultService;
import com.example.psbc.util.ConstantUtil;
import org.postgresql.copy.CopyIn;
import org.postgresql.core.BaseConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.SQLException;

@Service("postgresCopyService")
public class PostgresCopyServiceImpl implements PostgresCopyService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private SjjsImportDataResultService sjjsImportDataResultService;

    @Override
    public void importTxt(String filePath, String tableName, String columns, String delimiter, boolean hasHeader) throws SQLException, IOException {
        // 构建COPY命令
        StringBuilder sql = new StringBuilder();
        sql.append("COPY ").append(tableName).append("(")
                .append(columns)
                .append(") FROM STDIN WITH (FORMAT CSV");

        if (hasHeader) sql.append(", HEADER");
        if (!",".equals(delimiter)) sql.append(", DELIMITER '").append(delimiter).append("'");

        sql.append(")");

        jdbcTemplate.execute((Connection conn) -> {
            try (FileChannel fileChannel = FileChannel.open(
                    new File(filePath).toPath(),
                    StandardOpenOption.READ
            )) {
                BaseConnection pgConn = conn.unwrap(BaseConnection.class);
                CopyIn copyIn = pgConn.getCopyAPI().copyIn(sql.toString());
                ByteBuffer buffer = ByteBuffer.allocate(8192);
                while (fileChannel.read(buffer) > 0) {
                    buffer.flip(); // 切换为读模式
                    byte[] bytes = new byte[buffer.limit()];
                    buffer.get(bytes);
                    copyIn.writeToCopy(bytes, 0, bytes.length);
                    buffer.clear(); // 清空以便下次读取
                }

                long count = copyIn.endCopy();
                log.info("成功导入 %d 行数据", count);
                SjjsImportDataResult sjjsImportDataResult=new SjjsImportDataResult();
                sjjsImportDataResult.setTbNameEn(tableName);
                sjjsImportDataResult.setFlag(ConstantUtil.IMPORT_FLAG.SUCCESS.getValue());
                sjjsImportDataResult.setResultMsg("成功导入 "+count+" 行数据");
                sjjsImportDataResultService.insertSjjsImportDataResult(sjjsImportDataResult);
                return count;
            } catch (SQLException | IOException e) {
                throw new RuntimeException("导入TXT失败", e);
            }
        });
    }
}
