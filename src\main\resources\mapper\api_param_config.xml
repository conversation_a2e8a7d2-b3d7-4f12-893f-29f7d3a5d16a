<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.example.psbc.dao.ApiParamConfigDao" >
    <resultMap id="BaseResultMap" type="com.example.psbc.entity.ApiParamConfig" >
        <id column="id" property="id" jdbcType="DOUBLE" />
        <result column="url" property="url" jdbcType="VARCHAR" />
        <result column="cycle" property="cycle" jdbcType="VARCHAR" />
        <result column="exec_date" property="execDate" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="update_date" property="updateDate" jdbcType="DATE" />
        <result column="req_param" property="reqParam" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="tb_name" property="tbName" jdbcType="VARCHAR"/>
        <result column="file_create_path" property="fileCreatePath" jdbcType="VARCHAR" />
        <result column="file_target_path" property="fileTargetPath" jdbcType="VARCHAR" />
        <result column="pref_file_name" property="prefFileName" jdbcType="VARCHAR"/>
        <result column="get_way" property="getWay" jdbcType="VARCHAR"/>
        <result column="is_page" property="isPage" jdbcType="VARCHAR"/>
        <result column="count_url" property="countUrl" jdbcType="VARCHAR"/>
        <result column="task_info_url" property="taskInfoUrl" jdbcType="VARCHAR"/>
        <result column="page_size" property="pageSize" jdbcType="DOUBLE" />
        <result column="empty_file_flag" property="emptyFileFlag" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List">
        id,url,cycle,exec_date,status,update_date,req_param,
        tb_name,remark,file_create_path,file_target_path,pref_file_name,
        get_way,is_page,count_url,task_info_url,page_size,empty_file_flag
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Double" >
        select
        <include refid="Base_Column_List" />
        from T_API_PARAM_CONFIG
        where id = #{id,jdbcType=DOUBLE}
    </select>

    <select id="selectByTbName" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List" />
        from T_API_PARAM_CONFIG
        where TB_NAME = #{tbName,jdbcType=VARCHAR}
    </select>


    <select id="selectExecTaskList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_API_PARAM_CONFIG where status in ('0','2')  order by exec_date
    </select>

    <select id="selectListByParam" resultMap="BaseResultMap" parameterType="com.example.psbc.entity.ApiParamConfig">
        select
        <include refid="Base_Column_List"/>
        from T_API_PARAM_CONFIG
        where 1=1
        <if test="status != null">
            and status=#{status}
        </if>
        order by id
    </select>
    <update id="updateApiParamConfig" parameterType="com.example.psbc.entity.ApiParamConfig">
        update T_API_PARAM_CONFIG set
        <if test="execDate != null">
            exec_date=#{execDate},
        </if>
        <if test="status != null">
            status=#{status},
        </if>
        <if test="remark != null">
            remark=#{remark},
        </if>
        update_date=NOW()
        where
        id=#{id}
    </update>


</mapper>