package com.example.psbc.controller;

import com.example.psbc.services.DictConfigService;
import com.example.psbc.util.ConstantUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.TriggerContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * Created by xhj on 2022/3/28.
 */
@Configuration
//@EnableScheduling
public class ScheduleConfig{  /*implements SchedulingConfigurer{
      /**
     * 获取数据任务使用的线程池
     */
    @Value("${getData.corePoolSize}")
    private int GDCorePoolSize;
    @Value("${getData.queueSize}")
    private int GDQueueSize;
    @Value("${getData.maximumPoolSize}")
    private int GDMaximumPoolSize;
    @Value("${getData.keepAliveTime}")
    private int GDKeepAliveTime;
    /**
     * 导入数据任务使用的线程池
     */
    @Value("${importData.corePoolSize}")
    private int IDCorePoolSize;
    @Value("${importData.queueSize}")
    private int IDQueueSize;
    @Value("${importData.maximumPoolSize}")
    private int IDMaximumPoolSize;
    @Value("${importData.keepAliveTime}")
    private int IDKeepAliveTime;

    @Bean(name = "getDataTaskExecutor", destroyMethod = "shutdown")
    public ExecutorService getDataTaskExecutor() {
        // 使用有界队列
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(GDQueueSize);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                GDCorePoolSize,
                GDMaximumPoolSize,
                GDKeepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                new CustomizableThreadFactory("ImportData-"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 允许核心线程超时退出
        executor.allowCoreThreadTimeOut(true);

        return executor;
    }

    /**
     * 导入数据任务使用的线程池
     */
    @Bean(name = "importDataTaskExecutor", destroyMethod = "shutdown")
    public ExecutorService getImportDataTaskExecutor() {

        // 使用有界队列
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(IDQueueSize);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                IDCorePoolSize,
                IDMaximumPoolSize,
                IDKeepAliveTime,
                TimeUnit.SECONDS,
                workQueue,
                new CustomizableThreadFactory("ImportData-"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 允许核心线程超时退出
        executor.allowCoreThreadTimeOut(true);

        return executor;
    }

    @Bean(destroyMethod="shutdown")
    public Executor setExecutor(){
        return Executors.newScheduledThreadPool(5);
    }

}
