package com.example.psbc.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by xhj on 2021/11/12.
 */
public class DateUtil {
private static ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal=new ThreadLocal<>();

//SimpleDateFormat类是非线程安全
public static SimpleDateFormat getSimpleDateFormat(String pattern){
    if(simpleDateFormatThreadLocal.get()==null){
        simpleDateFormatThreadLocal.set(new SimpleDateFormat(pattern));
    }
    return simpleDateFormatThreadLocal.get();
}
//将字符串格式化成date
    public   static Date  formatToDate(String strDate){
        SimpleDateFormat sDateFormat;
        if(strDate.contains("-")){
            sDateFormat=new SimpleDateFormat("yyyy-MM-dd"); //getSimpleDateFormat("yyyy-MM-dd");
        }else{
            sDateFormat=new SimpleDateFormat("yyyyMMdd"); //getSimpleDateFormat("yyyyMMdd");
        }
        try {
            return sDateFormat.parse(strDate);
        } catch(Exception px){
            px.printStackTrace();
            return null;
        }
    }
//判断指定日期是否为月末日
public static boolean isLastDayOfMonth(String strDate){
    SimpleDateFormat sDateFormat=getSimpleDateFormat("yyyyMMdd");
    Calendar cd =Calendar.getInstance();
    try {
    //设置当前传入的时间，不设置默认为当前系统日期
    cd.setTime(sDateFormat.parse(strDate));
    cd.set(Calendar.DATE,cd.get(Calendar.DATE)+1);
    if(cd.get(Calendar.DAY_OF_MONTH)==1){
        return true;
    }else{
        return false;
    }
    } catch(Exception px){
        px.printStackTrace();
        return false;
    }
}


    /**
     *
     * @param strDate  日期(例如:20211001)
     * @param days  增加天数
     * @return
     */
    public   static String  addDay(String strDate,int  days){
        SimpleDateFormat sDateFormat=getSimpleDateFormat("yyyyMMdd");//new SimpleDateFormat("yyyyMMdd"); //加上时间
        try {
            Calendar cd =Calendar.getInstance();
            cd.setTime(sDateFormat.parse(strDate));
            cd.add(Calendar.DATE,days);
            return sDateFormat.format(cd.getTime());
        } catch(Exception px){
            px.printStackTrace();
            return null;
        }
    }

    /**
     *
     * @param strDate 月末日(例如:20210630,20210731,20210831)
     * @return  返回下月末日(例如:20210731,20210831,20210930)
     */
    public   static String  nextMonthEndDate(String strDate){
        SimpleDateFormat dft = getSimpleDateFormat("yyyyMMdd");//new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        try{
            if(strDate!=null && !"".equals(strDate)){
                calendar.setTime(dft.parse(strDate));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return dft.format(calendar.getTime());
    }
  //当前传入日期加一个月
   public   static String  addMonthDate(String strDate){
       SimpleDateFormat dft = getSimpleDateFormat("yyyyMMdd");//new SimpleDateFormat("yyyyMMdd");
       Calendar calendar = Calendar.getInstance();
       try{
           if(strDate!=null && !"".equals(strDate)){
               calendar.setTime(dft.parse(strDate));
           }
       }catch (Exception e){
           e.printStackTrace();
       }
       calendar.add(Calendar.MONTH, 1);
       return dft.format(calendar.getTime());
    }


    /**
     *
      * @param strDate
     * @return 返回下个季度末日(例如:20210331,20210630,20210930,20211231)
     */
    public   static String  nextQuarterEndDate(String strDate){
        SimpleDateFormat dft =getSimpleDateFormat("yyyyMMdd"); //new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        try{
            if(strDate!=null && !"".equals(strDate)){
                calendar.setTime(dft.parse(strDate));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        calendar.add(Calendar.MONTH, 3);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return dft.format(calendar.getTime());
    }



    /**
     *描述:传入的日期是否大于今天日期，大于今日返回true,小于等于返回false
     * @param strDate
     * @return
     */

    public  static boolean  isGtToday(String strDate){
        SimpleDateFormat sd = getSimpleDateFormat("yyyyMMdd");//new SimpleDateFormat("yyyyMMdd");
        Date date = new Date();
        boolean  is=true;
        try {
            is=date.before(sd.parse(strDate));
        }catch (Exception o){
            o.printStackTrace();
        }
        return is;
    }

}
