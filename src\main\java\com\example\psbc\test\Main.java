package com.example.psbc.test;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.example.psbc.entity.ApiParamConfig;
import com.example.psbc.util.ConstantUtil;
import com.example.psbc.util.DateUtil;

import javax.swing.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xmpsb-xiaohj on 2021/11/29.
 */
public class Main{
    private  static List<String>  res=new ArrayList();
    private static  ThreadLocal threadLocal=new ThreadLocal();
    static  String   count_url="https://*************:8084/bds-api/api/bds/countApi?servicecd=CD_XXX";
    public static void main(String[] args){

       /* String  data_url="https://*************:8084/bds-api/api/bds/extractApi?servicecd=CD_XXX&total=TOTAL_XXX&pageIndex=PI_XXX&pageSize=1000";
        String  url="";
        url=data_url.replace(ConstantUtil.URL_TOTAL_XXX,"2022").replace(ConstantUtil.URL_PI_XXX,"2022").replace("CD_XXX","2022");//替换总数量占位符
        System.out.println("data_url="+data_url);
        System.out.println("url="+url);*/
        String reqParam="and bds_etl_job_dt=TX_DATE_XXX";
        reqParam=reqParam.replace(ConstantUtil.PARAM_TX_DATE,"2023-04-30");
        HashMap<String, Object> paramMap=new HashMap<String, Object>();
        paramMap.put("uds",reqParam);
        String postBody = JSONObject.toJSONString(paramMap);
        System.out.println(postBody);
    }

public  static  void listAll(List list,String prefix){
//System.out.println(prefix);
    if(prefix.length()==3){
        res.add(prefix);
    }
for(int i=0;i<list.size();i++){

List temp=new LinkedList(list);
   //String  a= (String) temp.remove(i);
listAll(temp,prefix+temp.remove(i));

}


}




}
